

<?php $__env->startSection('content'); ?>
<div class="lw-page-content">
    <!-- Page header -->
    <div class="lw-page-header">
        <div class="container-fluid">
            <div class="row">
                <div class="col-sm-6">
                    <h3><?php echo e(__tr('Order Details')); ?> - #<?php echo e($order->order_id); ?></h3>
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('vendor.console')); ?>"><?php echo e(__tr('Home')); ?></a></li>
                        <li class="breadcrumb-item"><a href="<?php echo e(route('vendor.whatsapp.orders.list')); ?>"><?php echo e(__tr('WhatsApp Orders')); ?></a></li>
                        <li class="breadcrumb-item active"><?php echo e(__tr('Order Details')); ?></li>
                    </ol>
                </div>
                <div class="col-sm-6">
                    <div class="float-right">
                        <a href="<?php echo e(route('vendor.whatsapp.orders.list')); ?>" class="btn btn-secondary">
                            <i class="fa fa-arrow-left"></i> <?php echo e(__tr('Back to Orders')); ?>

                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Page content -->
    <div class="container-fluid">
        <div class="row">
            <!-- Order Information -->
            <div class="col-lg-8">
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><?php echo e(__tr('Order Information')); ?></h5>
                        <span class="badge badge-<?php echo e(getStatusColor($order->status)); ?> badge-lg">
                            <?php echo e($order->status_label); ?>

                        </span>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong><?php echo e(__tr('Order ID')); ?>:</strong></td>
                                        <td><?php echo e($order->order_id); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong><?php echo e(__tr('Order Date')); ?>:</strong></td>
                                        <td><?php echo e($order->created_at->format('d M Y, h:i A')); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong><?php echo e(__tr('Payment Status')); ?>:</strong></td>
                                        <td>
                                            <?php if($order->payment_status): ?>
                                                <span class="badge badge-success"><?php echo e(ucfirst($order->payment_status)); ?></span>
                                            <?php else: ?>
                                                <span class="badge badge-warning"><?php echo e(__tr('Pending')); ?></span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong><?php echo e(__tr('Currency')); ?>:</strong></td>
                                        <td><?php echo e($order->currency); ?></td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong><?php echo e(__tr('Total Amount')); ?>:</strong></td>
                                        <td><h5 class="text-success mb-0"><?php echo e($order->formatted_total_amount); ?></h5></td>
                                    </tr>
                                    <tr>
                                        <td><strong><?php echo e(__tr('Payment Completed')); ?>:</strong></td>
                                        <td>
                                            <?php if($order->payment_completed_at): ?>
                                                <?php echo e($order->payment_completed_at->format('d M Y, h:i A')); ?>

                                            <?php else: ?>
                                                <?php echo e(__tr('Not completed')); ?>

                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong><?php echo e(__tr('Shipped At')); ?>:</strong></td>
                                        <td>
                                            <?php if($order->shipped_at): ?>
                                                <?php echo e($order->shipped_at->format('d M Y, h:i A')); ?>

                                            <?php else: ?>
                                                <?php echo e(__tr('Not shipped')); ?>

                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong><?php echo e(__tr('Delivered At')); ?>:</strong></td>
                                        <td>
                                            <?php if($order->delivered_at): ?>
                                                <?php echo e($order->delivered_at->format('d M Y, h:i A')); ?>

                                            <?php else: ?>
                                                <?php echo e(__tr('Not delivered')); ?>

                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Order Items -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><?php echo e(__tr('Order Items')); ?></h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th><?php echo e(__tr('Product')); ?></th>
                                        <th><?php echo e(__tr('Quantity')); ?></th>
                                        <th><?php echo e(__tr('Price')); ?></th>
                                        <th><?php echo e(__tr('Total')); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if($order->items): ?>
                                        <?php $__currentLoopData = $order->items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td>
                                                <div>
                                                    <strong><?php echo e($item['product_retailer_id'] ?? $item['name'] ?? __tr('Unknown Product')); ?></strong>
                                                </div>
                                                <?php if(isset($item['product_description'])): ?>
                                                    <small class="text-muted"><?php echo e($item['product_description']); ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo e($item['quantity'] ?? 1); ?></td>
                                            <td><?php echo e($order->currency); ?> <?php echo e(number_format($item['item_price'] ?? 0, 2)); ?></td>
                                            <td><?php echo e($order->currency); ?> <?php echo e(number_format(($item['item_price'] ?? 0) * ($item['quantity'] ?? 1), 2)); ?></td>
                                        </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <?php endif; ?>
                                </tbody>
                                <tfoot>
                                    <tr>
                                        <th colspan="3"><?php echo e(__tr('Subtotal')); ?></th>
                                        <th><?php echo e($order->currency); ?> <?php echo e(number_format($order->total_amount, 2)); ?></th>
                                    </tr>
                                    <?php if($order->tax_amount > 0): ?>
                                    <tr>
                                        <th colspan="3"><?php echo e(__tr('Tax')); ?></th>
                                        <th><?php echo e($order->currency); ?> <?php echo e(number_format($order->tax_amount, 2)); ?></th>
                                    </tr>
                                    <?php endif; ?>
                                    <?php if($order->shipping_amount > 0): ?>
                                    <tr>
                                        <th colspan="3"><?php echo e(__tr('Shipping')); ?></th>
                                        <th><?php echo e($order->currency); ?> <?php echo e(number_format($order->shipping_amount, 2)); ?></th>
                                    </tr>
                                    <?php endif; ?>
                                    <?php if($order->discount_amount > 0): ?>
                                    <tr>
                                        <th colspan="3"><?php echo e(__tr('Discount')); ?></th>
                                        <th>-<?php echo e($order->currency); ?> <?php echo e(number_format($order->discount_amount, 2)); ?></th>
                                    </tr>
                                    <?php endif; ?>
                                    <tr class="table-success">
                                        <th colspan="3"><?php echo e(__tr('Total')); ?></th>
                                        <th><?php echo e($order->currency); ?> <?php echo e(number_format($order->getFinalAmount(), 2)); ?></th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Payment History -->
                <?php if($payments->count() > 0): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><?php echo e(__tr('Payment History')); ?></h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th><?php echo e(__tr('Payment ID')); ?></th>
                                        <th><?php echo e(__tr('Amount')); ?></th>
                                        <th><?php echo e(__tr('Status')); ?></th>
                                        <th><?php echo e(__tr('Method')); ?></th>
                                        <th><?php echo e(__tr('Date')); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $payments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $payment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td><code><?php echo e($payment->payment_id); ?></code></td>
                                        <td><?php echo e($payment->formatted_amount); ?></td>
                                        <td>
                                            <span class="badge badge-<?php echo e($payment->is_successful ? 'success' : ($payment->is_failed ? 'danger' : 'warning')); ?>">
                                                <?php echo e($payment->status_label); ?>

                                            </span>
                                        </td>
                                        <td><?php echo e($payment->payment_method ?? __tr('N/A')); ?></td>
                                        <td><?php echo e($payment->created_at->format('d M Y, h:i A')); ?></td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Customer Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><?php echo e(__tr('Customer Information')); ?></h5>
                    </div>
                    <div class="card-body">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong><?php echo e(__tr('Name')); ?>:</strong></td>
                                <td><?php echo e($order->customer_name ?: __tr('N/A')); ?></td>
                            </tr>
                            <tr>
                                <td><strong><?php echo e(__tr('Phone')); ?>:</strong></td>
                                <td>
                                    <a href="https://wa.me/<?php echo e($order->customer_phone); ?>" target="_blank" class="text-success">
                                        <i class="fab fa-whatsapp"></i> <?php echo e($order->customer_phone); ?>

                                    </a>
                                </td>
                            </tr>
                            <?php if($order->contact): ?>
                            <tr>
                                <td><strong><?php echo e(__tr('Contact')); ?>:</strong></td>
                                <td>
                                    <a href="<?php echo e(route('vendor.chat_message.contact.view', $order->contact->_uid)); ?>" class="btn btn-sm btn-outline-primary">
                                        <?php echo e(__tr('Chat with Customer')); ?>

                                    </a>
                                </td>
                            </tr>
                            <?php endif; ?>
                        </table>
                    </div>
                </div>

                <!-- Delivery Address -->
                <?php if($order->delivery_address): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><?php echo e(__tr('Delivery Address')); ?></h5>
                    </div>
                    <div class="card-body">
                        <address>
                            <?php echo nl2br(e($order->delivery_address)); ?>

                        </address>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Order Actions -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><?php echo e(__tr('Order Actions')); ?></h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button type="button" class="btn btn-warning btn-block" onclick="showUpdateStatusModal()">
                                <i class="fa fa-edit"></i> <?php echo e(__tr('Update Status')); ?>

                            </button>
                            
                            <?php if($order->canBeCancelled()): ?>
                            <button type="button" class="btn btn-danger btn-block" onclick="cancelOrder()">
                                <i class="fa fa-times"></i> <?php echo e(__tr('Cancel Order')); ?>

                            </button>
                            <?php endif; ?>
                            
                            <a href="https://wa.me/<?php echo e($order->customer_phone); ?>?text=<?php echo e(urlencode('Hi! Regarding your order #' . $order->order_id)); ?>" 
                               target="_blank" class="btn btn-success btn-block">
                                <i class="fab fa-whatsapp"></i> <?php echo e(__tr('Message Customer')); ?>

                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Order Status Update Modal -->
<div class="modal fade" id="updateStatusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><?php echo e(__tr('Update Order Status')); ?></h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="updateStatusForm">
                <div class="modal-body">
                    <div class="form-group">
                        <label><?php echo e(__tr('Current Status')); ?></label>
                        <input type="text" class="form-control" value="<?php echo e($order->status_label); ?>" readonly>
                    </div>
                    <div class="form-group">
                        <label><?php echo e(__tr('New Status')); ?></label>
                        <select name="status" class="form-control" required>
                            <option value="pending" <?php echo e($order->status == 'pending' ? 'selected' : ''); ?>><?php echo e(__tr('Pending')); ?></option>
                            <option value="awaiting_address" <?php echo e($order->status == 'awaiting_address' ? 'selected' : ''); ?>><?php echo e(__tr('Awaiting Address')); ?></option>
                            <option value="awaiting_payment" <?php echo e($order->status == 'awaiting_payment' ? 'selected' : ''); ?>><?php echo e(__tr('Awaiting Payment')); ?></option>
                            <option value="paid" <?php echo e($order->status == 'paid' ? 'selected' : ''); ?>><?php echo e(__tr('Paid')); ?></option>
                            <option value="confirmed" <?php echo e($order->status == 'confirmed' ? 'selected' : ''); ?>><?php echo e(__tr('Confirmed')); ?></option>
                            <option value="shipped" <?php echo e($order->status == 'shipped' ? 'selected' : ''); ?>><?php echo e(__tr('Shipped')); ?></option>
                            <option value="delivered" <?php echo e($order->status == 'delivered' ? 'selected' : ''); ?>><?php echo e(__tr('Delivered')); ?></option>
                            <option value="cancelled" <?php echo e($order->status == 'cancelled' ? 'selected' : ''); ?>><?php echo e(__tr('Cancelled')); ?></option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal"><?php echo e(__tr('Cancel')); ?></button>
                    <button type="submit" class="btn btn-primary"><?php echo e(__tr('Update Status')); ?></button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('appScripts'); ?>
<script>
$(document).ready(function() {
    // Setup CSRF token for all AJAX requests
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    // Status update form submission
    $('#updateStatusForm').on('submit', function(e) {
        e.preventDefault();
        updateOrderStatus();
    });
});

function showUpdateStatusModal() {
    $('#updateStatusModal').modal('show');
}

function updateOrderStatus() {
    const formData = $('#updateStatusForm').serialize();
    
    $.ajax({
        url: "<?php echo e(route('vendor.whatsapp.orders.update_status', $order->_uid)); ?>",
        method: 'PATCH',
        data: formData,
        success: function(response) {
            if (response.reaction == 1) {
                $('#updateStatusModal').modal('hide');
                showSuccessMessage('<?php echo e(__tr("Order status updated successfully")); ?>');
                setTimeout(function() {
                    location.reload();
                }, 1500);
            } else if (response.reaction == 2 && response.data.message === 'Token Expired, Please reload and try again.') {
                showErrorMessage('<?php echo e(__tr("Session expired. The page will refresh.")); ?>');
                setTimeout(function() {
                    window.location.reload();
                }, 2000);
            } else {
                showErrorMessage(response.data.message || '<?php echo e(__tr("Failed to update order status")); ?>');
            }
        },
        error: function() {
            showErrorMessage('<?php echo e(__tr("Error updating order status")); ?>');
        }
    });
}

function cancelOrder() {
    if (confirm('<?php echo e(__tr("Are you sure you want to cancel this order?")); ?>')) {
        $.ajax({
            url: "<?php echo e(route('vendor.whatsapp.orders.update_status', $order->_uid)); ?>",
            method: 'PATCH',
            data: { status: 'cancelled' },
            success: function(response) {
                if (response.reaction == 1) {
                    showSuccessMessage('<?php echo e(__tr("Order cancelled successfully")); ?>');
                    setTimeout(function() {
                        location.reload();
                    }, 1500);
                } else if (response.reaction == 2 && response.data.message === 'Token Expired, Please reload and try again.') {
                    showErrorMessage('<?php echo e(__tr("Session expired. The page will refresh.")); ?>');
                    setTimeout(function() {
                        window.location.reload();
                    }, 2000);
                } else {
                    showErrorMessage(response.data.message || '<?php echo e(__tr("Failed to cancel order")); ?>');
                }
            },
            error: function() {
                showErrorMessage('<?php echo e(__tr("Error cancelling order")); ?>');
            }
        });
    }
}
</script>
<?php $__env->stopPush(); ?>

<?php
function getStatusColor($status) {
    $colors = [
        'pending' => 'warning',
        'awaiting_address' => 'info',
        'awaiting_payment' => 'warning',
        'paid' => 'success',
        'confirmed' => 'success',
        'shipped' => 'primary',
        'delivered' => 'success',
        'cancelled' => 'danger',
        'refunded' => 'secondary'
    ];
    return $colors[$status] ?? 'secondary';
}
?>

<?php echo $__env->make('layouts.app', ['title' => __tr('Order Details - :orderID', ['orderID' => $order->order_id])], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\omx-flow-new\resources\views/whatsapp-service/order-details.blade.php ENDPATH**/ ?>